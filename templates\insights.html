{% extends "base.html" %}

{% block title %}Market Insights - Used Cars Price Predictor{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-12">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-chart-bar text-warning"></i>
                    Market Insights
                </h1>
                <p class="lead">
                    Discover trends and patterns in the used car market
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Key Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold text-primary mb-3">Market Overview</h2>
                <p class="lead text-muted">Key statistics from our analysis of 3M+ car listings</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-3">
                <div class="insight-card text-center">
                    <i class="fas fa-car fa-3x text-primary mb-3"></i>
                    <h3 class="fw-bold text-primary">3M+</h3>
                    <p class="text-muted">Car Listings Analyzed</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="insight-card text-center">
                    <i class="fas fa-dollar-sign fa-3x text-success mb-3"></i>
                    <h3 class="fw-bold text-success">$29,847</h3>
                    <p class="text-muted">Average Car Price</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="insight-card text-center">
                    <i class="fas fa-calendar-alt fa-3x text-info mb-3"></i>
                    <h3 class="fw-bold text-info">2015</h3>
                    <p class="text-muted">Average Car Year</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="insight-card text-center">
                    <i class="fas fa-road fa-3x text-warning mb-3"></i>
                    <h3 class="fw-bold text-warning">67,432</h3>
                    <p class="text-muted">Average Mileage</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Charts Section -->
<section class="py-5 bg-light">
    <div class="container">
        <!-- Price Distribution Chart -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="insight-card">
                    <h4><i class="fas fa-chart-histogram"></i> Price Distribution</h4>
                    <p class="text-muted">Distribution of car prices in our dataset</p>
                    <div class="chart-container">
                        <canvas id="priceDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Top Brands Chart -->
        <div class="row mb-5">
            <div class="col-md-6">
                <div class="insight-card">
                    <h4><i class="fas fa-trophy"></i> Top Car Brands</h4>
                    <p class="text-muted">Most popular car brands by listing count</p>
                    <div class="chart-container">
                        <canvas id="topBrandsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="insight-card">
                    <h4><i class="fas fa-gas-pump"></i> Fuel Economy vs Price</h4>
                    <p class="text-muted">Relationship between fuel efficiency and price</p>
                    <div class="chart-container">
                        <canvas id="fuelEconomyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Age vs Price Chart -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="insight-card">
                    <h4><i class="fas fa-clock"></i> Depreciation Analysis</h4>
                    <p class="text-muted">How car prices change with age</p>
                    <div class="chart-container">
                        <canvas id="depreciationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Key Insights Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold text-primary mb-3">Key Market Insights</h2>
                <p class="lead text-muted">What our AI model learned from the data</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="insight-card text-center h-100">
                    <i class="fas fa-trending-down fa-3x text-danger mb-3"></i>
                    <h5 class="fw-bold">Depreciation Patterns</h5>
                    <p class="text-muted">
                        Cars lose approximately 15-20% of their value each year, with luxury brands 
                        depreciating faster than economy brands.
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="insight-card text-center h-100">
                    <i class="fas fa-leaf fa-3x text-success mb-3"></i>
                    <h5 class="fw-bold">Fuel Efficiency Premium</h5>
                    <p class="text-muted">
                        Hybrid and electric vehicles command a 10-15% price premium, 
                        especially in urban markets.
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="insight-card text-center h-100">
                    <i class="fas fa-star fa-3x text-warning mb-3"></i>
                    <h5 class="fw-bold">Brand Value</h5>
                    <p class="text-muted">
                        Luxury brands (BMW, Mercedes, Audi) maintain higher resale values 
                        compared to economy brands.
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="insight-card text-center h-100">
                    <i class="fas fa-road fa-3x text-info mb-3"></i>
                    <h5 class="fw-bold">Mileage Impact</h5>
                    <p class="text-muted">
                        Every 10,000 miles typically reduces car value by 2-3%, 
                        with diminishing impact at higher mileages.
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="insight-card text-center h-100">
                    <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                    <h5 class="fw-bold">Geographic Variations</h5>
                    <p class="text-muted">
                        Car prices vary by region, with coastal areas typically 
                        showing 5-10% higher prices than inland markets.
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="insight-card text-center h-100">
                    <i class="fas fa-calendar-check fa-3x text-secondary mb-3"></i>
                    <h5 class="fw-bold">Seasonal Trends</h5>
                    <p class="text-muted">
                        Convertibles peak in spring/summer, while SUVs and trucks 
                        see higher demand in fall/winter months.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Model Performance Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold text-primary mb-3">Model Performance</h2>
                <p class="lead text-muted">How our AI achieves 95.5% accuracy</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6">
                <div class="insight-card">
                    <h4><i class="fas fa-bullseye"></i> Accuracy Metrics</h4>
                    <div class="row text-center mt-4">
                        <div class="col-6">
                            <h3 class="text-success">95.5%</h3>
                            <p class="text-muted">R² Score</p>
                        </div>
                        <div class="col-6">
                            <h3 class="text-info">$2,215</h3>
                            <p class="text-muted">Mean Absolute Error</p>
                        </div>
                    </div>
                    <div class="row text-center">
                        <div class="col-6">
                            <h3 class="text-warning">$3,294</h3>
                            <p class="text-muted">Root Mean Square Error</p>
                        </div>
                        <div class="col-6">
                            <h3 class="text-primary">7.4%</h3>
                            <p class="text-muted">Average Error Rate</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="insight-card">
                    <h4><i class="fas fa-cogs"></i> Feature Importance</h4>
                    <div class="mt-4">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Vehicle Age</span>
                                <span class="fw-bold">28%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-primary" style="width: 28%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Make & Model</span>
                                <span class="fw-bold">22%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 22%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Mileage</span>
                                <span class="fw-bold">18%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 18%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Performance Specs</span>
                                <span class="fw-bold">15%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: 15%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Other Features</span>
                                <span class="fw-bold">17%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-secondary" style="width: 17%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Price Distribution Chart
    const priceData = {
        labels: ['$0-10k', '$10-20k', '$20-30k', '$30-40k', '$40-50k', '$50k+'],
        datasets: [{
            label: 'Number of Cars',
            data: [450000, 820000, 680000, 420000, 280000, 320000],
            backgroundColor: 'rgba(102, 126, 234, 0.8)',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 1
        }]
    };
    
    CarPricePro.createChart('priceDistributionChart', 'bar', priceData, {
        scales: {
            y: {
                title: {
                    display: true,
                    text: 'Number of Listings'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Price Range'
                }
            }
        }
    });
    
    // Top Brands Chart
    const brandsData = {
        labels: ['Ford', 'Chevrolet', 'Toyota', 'Honda', 'Nissan', 'BMW', 'Mercedes', 'Audi'],
        datasets: [{
            data: [380000, 340000, 320000, 280000, 220000, 180000, 160000, 140000],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ]
        }]
    };
    
    CarPricePro.createChart('topBrandsChart', 'doughnut', brandsData);
    
    // Fuel Economy Chart
    const fuelData = {
        datasets: [{
            label: 'Cars',
            data: [
                {x: 15, y: 45000}, {x: 20, y: 38000}, {x: 25, y: 32000},
                {x: 30, y: 28000}, {x: 35, y: 25000}, {x: 40, y: 35000}
            ],
            backgroundColor: 'rgba(40, 167, 69, 0.6)',
            borderColor: 'rgba(40, 167, 69, 1)'
        }]
    };
    
    CarPricePro.createChart('fuelEconomyChart', 'scatter', fuelData, {
        scales: {
            x: {
                title: {
                    display: true,
                    text: 'Fuel Economy (MPG)'
                }
            },
            y: {
                title: {
                    display: true,
                    text: 'Average Price ($)'
                }
            }
        }
    });
    
    // Depreciation Chart
    const depreciationData = {
        labels: ['0-1 years', '1-3 years', '3-5 years', '5-7 years', '7-10 years', '10+ years'],
        datasets: [{
            label: 'Average Price ($)',
            data: [42000, 35000, 28000, 22000, 16000, 12000],
            borderColor: 'rgba(220, 53, 69, 1)',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            fill: true,
            tension: 0.4
        }]
    };
    
    CarPricePro.createChart('depreciationChart', 'line', depreciationData, {
        scales: {
            y: {
                title: {
                    display: true,
                    text: 'Average Price ($)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Vehicle Age'
                }
            }
        }
    });
}
</script>
{% endblock %}
