#!/usr/bin/env python3
"""
Model Training and Serialization Module
Trains the LightGBM model and saves all components for web application use
"""

import pandas as pd
import numpy as np
import pickle
import os
import warnings
from datetime import datetime
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb

def load_and_prepare_data():
    """Load and prepare the dataset"""
    print("1. Loading dataset...")
    df_path = r"C:\Users\<USER>\.cache\kagglehub\datasets\ananaymital\us-used-cars-dataset\versions\1\used_cars_data.csv"
    cars_df = pd.read_csv(df_path, low_memory=False)
    print(f"✅ Dataset loaded: {cars_df.shape}")
    
    # Initial cleaning
    print("\n2. Initial data cleaning...")
    print(f"Original size: {len(cars_df):,}")
    
    # Remove missing prices
    cars_df = cars_df.dropna(subset=['price'])
    print(f"After removing missing prices: {len(cars_df):,}")
    
    # Remove extreme outliers (0.5th to 99.5th percentile)
    Q1 = cars_df['price'].quantile(0.005)
    Q99 = cars_df['price'].quantile(0.995)
    cars_df = cars_df[(cars_df['price'] >= Q1) & (cars_df['price'] <= Q99)]
    print(f"After removing outliers: {len(cars_df):,}")
    
    # Drop columns with >80% missing data
    missing_threshold = 0.8
    missing_data = cars_df.isnull().sum()
    missing_percent = (missing_data / len(cars_df))
    cols_to_drop = missing_percent[missing_percent > missing_threshold].index.tolist()
    cars_df = cars_df.drop(columns=cols_to_drop)
    print(f"Dropped {len(cols_to_drop)} columns with >80% missing data")
    
    return cars_df

def engineer_features(cars_df):
    """Create engineered features"""
    print("\n3. Feature engineering...")
    
    # Create age feature
    current_year = 2021
    cars_df['age'] = current_year - cars_df['year']
    
    # Handle missing values for key numerical features
    numerical_features = ['mileage', 'horsepower', 'engine_displacement', 
                         'city_fuel_economy', 'highway_fuel_economy', 'daysonmarket',
                         'latitude', 'longitude', 'seller_rating']
    
    for col in numerical_features:
        if col in cars_df.columns:
            cars_df[col] = cars_df[col].fillna(cars_df[col].median())
    
    # Handle missing values for key categorical features
    key_categorical = ['make_name', 'model_name', 'body_type', 'fuel_type', 'transmission', 'exterior_color']
    for col in key_categorical:
        if col in cars_df.columns:
            cars_df[col] = cars_df[col].fillna('Unknown')
    
    # Handle franchise_dealer
    if 'franchise_dealer' in cars_df.columns:
        cars_df['franchise_dealer'] = cars_df['franchise_dealer'].fillna(True)
    
    # Create engineered features
    if 'mileage' in cars_df.columns and 'age' in cars_df.columns:
        cars_df['mileage_per_year'] = cars_df['mileage'] / (cars_df['age'] + 1)
    
    if 'horsepower' in cars_df.columns and 'engine_displacement' in cars_df.columns:
        cars_df['power_to_displacement'] = cars_df['horsepower'] / (cars_df['engine_displacement'] + 1)
    
    if 'city_fuel_economy' in cars_df.columns and 'highway_fuel_economy' in cars_df.columns:
        cars_df['avg_fuel_economy'] = (cars_df['city_fuel_economy'] + cars_df['highway_fuel_economy']) / 2
    
    print("✅ Feature engineering completed")
    return cars_df

def prepare_features(cars_df):
    """Select and prepare features for modeling"""
    print("\n4. Feature selection...")
    
    feature_columns = [
        # Numerical features
        'mileage', 'age', 'horsepower', 'engine_displacement', 
        'city_fuel_economy', 'highway_fuel_economy', 'avg_fuel_economy',
        'mileage_per_year', 'power_to_displacement', 'daysonmarket',
        'latitude', 'longitude', 'seller_rating', 'year',
        
        # Categorical features
        'make_name', 'model_name', 'body_type', 'fuel_type', 'transmission',
        'exterior_color', 'franchise_dealer'
    ]
    
    # Keep only available columns
    available_features = [col for col in feature_columns if col in cars_df.columns]
    X = cars_df[available_features].copy()
    y = cars_df['price'].copy()
    
    print(f"Selected {len(available_features)} features")
    print(f"Final dataset: {X.shape}")
    
    return X, y, available_features

def preprocess_data(X_train, X_test):
    """Preprocess the data with proper train/test split handling"""
    print("\n5. Data preprocessing...")
    
    # Identify column types
    categorical_cols = X_train.select_dtypes(include=['object', 'category', 'bool']).columns.tolist()
    numerical_cols = X_train.select_dtypes(include=[np.number]).columns.tolist()
    
    # Handle missing values using only training data statistics
    # Numerical imputation
    numerical_imputer = SimpleImputer(strategy='median')
    X_train_num = pd.DataFrame(
        numerical_imputer.fit_transform(X_train[numerical_cols]),
        columns=numerical_cols,
        index=X_train.index
    )
    X_test_num = pd.DataFrame(
        numerical_imputer.transform(X_test[numerical_cols]),
        columns=numerical_cols,
        index=X_test.index
    )
    
    # Categorical imputation
    categorical_imputer = SimpleImputer(strategy='constant', fill_value='Unknown')
    X_train_cat = pd.DataFrame(
        categorical_imputer.fit_transform(X_train[categorical_cols]),
        columns=categorical_cols,
        index=X_train.index
    )
    X_test_cat = pd.DataFrame(
        categorical_imputer.transform(X_test[categorical_cols]),
        columns=categorical_cols,
        index=X_test.index
    )
    
    # Combine features
    X_train_clean = pd.concat([X_train_num, X_train_cat], axis=1)
    X_test_clean = pd.concat([X_test_num, X_test_cat], axis=1)
    
    # Encode categorical variables
    X_train_encoded = X_train_clean.copy()
    X_test_encoded = X_test_clean.copy()
    label_encoders = {}
    
    for col in categorical_cols:
        le = LabelEncoder()
        X_train_encoded[col] = le.fit_transform(X_train_encoded[col].astype(str))
        
        # Handle unseen labels in test set
        test_values = X_test_encoded[col].astype(str)
        unseen_mask = ~test_values.isin(le.classes_)
        if unseen_mask.any():
            most_frequent = X_train_clean[col].mode()[0] if not X_train_clean[col].mode().empty else 'Unknown'
            test_values[unseen_mask] = str(most_frequent)
        
        X_test_encoded[col] = le.transform(test_values)
        label_encoders[col] = le
    
    print("✅ Data preprocessing completed")
    
    return X_train_encoded, X_test_encoded, label_encoders, numerical_imputer, categorical_imputer

def train_model(X_train, X_test, y_train, y_test):
    """Train the LightGBM model"""
    print("\n6. Training LightGBM model...")
    
    lgb_params = {
        'objective': 'regression',
        'metric': 'rmse',
        'boosting_type': 'gbdt',
        'num_leaves': 150,
        'learning_rate': 0.1,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42
    }
    
    # Create validation split from training data
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    train_data = lgb.Dataset(X_train_split, label=y_train_split)
    valid_data = lgb.Dataset(X_val_split, label=y_val_split, reference=train_data)
    
    lgb_model = lgb.train(
        lgb_params,
        train_data,
        valid_sets=[train_data, valid_data],
        num_boost_round=1000,
        callbacks=[lgb.early_stopping(50), lgb.log_evaluation(100)]
    )
    
    print("✅ Model training completed")
    return lgb_model

def evaluate_model(model, X_train, X_test, y_train, y_test):
    """Evaluate the trained model"""
    print("\n7. Model evaluation...")
    
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    train_r2 = r2_score(y_train, y_pred_train)
    test_r2 = r2_score(y_test, y_pred_test)
    test_mae = mean_absolute_error(y_test, y_pred_test)
    test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
    
    print(f"\n{'='*60}")
    print("🎉 FINAL RESULTS")
    print(f"{'='*60}")
    print(f"Training R²:     {train_r2:.4f}")
    print(f"Test R²:         {test_r2:.4f}")
    print(f"Test MAE:        ${test_mae:.2f}")
    print(f"Test RMSE:       ${test_rmse:.2f}")
    print(f"{'='*60}")
    
    return {
        'train_r2': train_r2,
        'test_r2': test_r2,
        'test_mae': test_mae,
        'test_rmse': test_rmse,
        'training_date': datetime.now().isoformat()
    }

def save_model_components(model, label_encoders, feature_columns, model_metadata, 
                         numerical_imputer, categorical_imputer):
    """Save all model components for web application"""
    print("\n8. Saving model components...")
    
    # Create models directory
    os.makedirs('models', exist_ok=True)
    
    # Save model
    with open('models/lgb_model.pkl', 'wb') as f:
        pickle.dump(model, f)
    
    # Save label encoders
    with open('models/label_encoders.pkl', 'wb') as f:
        pickle.dump(label_encoders, f)
    
    # Save feature columns
    with open('models/feature_columns.pkl', 'wb') as f:
        pickle.dump(feature_columns, f)
    
    # Save model metadata
    with open('models/model_metadata.pkl', 'wb') as f:
        pickle.dump(model_metadata, f)
    
    # Save imputers
    with open('models/numerical_imputer.pkl', 'wb') as f:
        pickle.dump(numerical_imputer, f)
    
    with open('models/categorical_imputer.pkl', 'wb') as f:
        pickle.dump(categorical_imputer, f)
    
    print("✅ All model components saved successfully")

def main():
    """Main training pipeline"""
    print("=== USED CARS PRICE PREDICTION MODEL TRAINING ===")
    print("🎯 Goal: Train and save model for web application")
    print()
    
    # Load and prepare data
    cars_df = load_and_prepare_data()
    cars_df = engineer_features(cars_df)
    X, y, feature_columns = prepare_features(cars_df)
    
    # Train/test split
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Preprocess data
    X_train_encoded, X_test_encoded, label_encoders, numerical_imputer, categorical_imputer = preprocess_data(X_train, X_test)
    
    # Train model
    model = train_model(X_train_encoded, X_test_encoded, y_train, y_test)
    
    # Evaluate model
    model_metadata = evaluate_model(model, X_train_encoded, X_test_encoded, y_train, y_test)
    model_metadata['feature_columns'] = feature_columns
    
    # Save all components
    save_model_components(model, label_encoders, feature_columns, model_metadata, 
                         numerical_imputer, categorical_imputer)
    
    print("\n🎉 Model training and saving completed successfully!")
    print("The web application can now use the trained model for predictions.")

if __name__ == '__main__':
    main()
