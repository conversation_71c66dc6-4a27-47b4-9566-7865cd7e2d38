#!/usr/bin/env python3
"""
Test script to verify the web application components work correctly
"""

import os
import pickle
import pandas as pd
import numpy as np

def test_model_loading():
    """Test if the trained model and components can be loaded"""
    print("Testing model loading...")
    
    try:
        # Check if model files exist
        model_files = [
            'models/lgb_model.pkl',
            'models/label_encoders.pkl', 
            'models/feature_columns.pkl',
            'models/model_metadata.pkl'
        ]
        
        for file_path in model_files:
            if not os.path.exists(file_path):
                print(f"❌ Missing file: {file_path}")
                return False
            else:
                print(f"✅ Found: {file_path}")
        
        # Load model components
        with open('models/lgb_model.pkl', 'rb') as f:
            model = pickle.load(f)
        
        with open('models/label_encoders.pkl', 'rb') as f:
            label_encoders = pickle.load(f)
        
        with open('models/feature_columns.pkl', 'rb') as f:
            feature_columns = pickle.load(f)
        
        with open('models/model_metadata.pkl', 'rb') as f:
            model_metadata = pickle.load(f)
        
        print(f"✅ Model loaded successfully")
        print(f"✅ Model R² score: {model_metadata.get('test_r2', 'N/A')}")
        print(f"✅ Number of features: {len(feature_columns)}")
        print(f"✅ Number of label encoders: {len(label_encoders)}")
        
        return True, model, label_encoders, feature_columns, model_metadata
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False, None, None, None, None

def test_prediction():
    """Test making a prediction"""
    print("\nTesting prediction functionality...")
    
    success, model, label_encoders, feature_columns, model_metadata = test_model_loading()
    if not success:
        return False
    
    try:
        # Create test input
        test_make = 'Toyota'
        test_model = 'Camry'
        test_year = 2018
        test_mileage = 50000
        
        # Create prediction input (simplified version)
        current_year = 2021
        age = current_year - test_year
        
        # Create feature dictionary with defaults
        features = {
            'mileage': test_mileage,
            'age': age,
            'year': test_year,
            'make_name': test_make,
            'model_name': test_model,
            'horsepower': 200,
            'engine_displacement': 2.5,
            'city_fuel_economy': 28,
            'highway_fuel_economy': 35,
            'daysonmarket': 30,
            'latitude': 39.0,
            'longitude': -98.0,
            'seller_rating': 4.2,
            'body_type': 'Sedan',
            'fuel_type': 'Gasoline',
            'transmission': 'Automatic',
            'exterior_color': 'White',
            'franchise_dealer': True
        }
        
        # Calculate engineered features
        features['mileage_per_year'] = test_mileage / (age + 1)
        features['power_to_displacement'] = features['horsepower'] / (features['engine_displacement'] + 1)
        features['avg_fuel_economy'] = (features['city_fuel_economy'] + features['highway_fuel_economy']) / 2
        
        # Convert to feature vector
        feature_vector = []
        for col in feature_columns:
            if col in features:
                value = features[col]
                # Apply label encoding for categorical variables
                if col in label_encoders and isinstance(value, str):
                    try:
                        value = label_encoders[col].transform([str(value)])[0]
                    except:
                        value = 0
                elif col in label_encoders and isinstance(value, bool):
                    value = int(value)
                feature_vector.append(float(value))
            else:
                feature_vector.append(0.0)
        
        # Make prediction
        predicted_price = model.predict([feature_vector])[0]
        
        print(f"✅ Prediction successful!")
        print(f"   Input: {test_year} {test_make} {test_model} with {test_mileage:,} miles")
        print(f"   Predicted price: ${predicted_price:,.2f}")
        
        # Sanity check
        if 5000 <= predicted_price <= 100000:
            print(f"✅ Prediction seems reasonable")
            return True
        else:
            print(f"⚠️  Prediction seems unusual (${predicted_price:,.2f})")
            return True  # Still successful, just unusual
            
    except Exception as e:
        print(f"❌ Error making prediction: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_access():
    """Test if we can access the original dataset for unique values"""
    print("\nTesting data access...")
    
    try:
        df_path = r"C:\Users\<USER>\.cache\kagglehub\datasets\ananaymital\us-used-cars-dataset\versions\1\used_cars_data.csv"
        
        if not os.path.exists(df_path):
            print(f"❌ Dataset not found at: {df_path}")
            return False
        
        # Load a small sample
        cars_df = pd.read_csv(df_path, nrows=1000, low_memory=False)
        
        if 'make_name' in cars_df.columns:
            unique_makes = cars_df['make_name'].dropna().unique()
            print(f"✅ Found {len(unique_makes)} unique makes in sample")
            print(f"   Sample makes: {list(unique_makes)[:5]}")
        
        if 'model_name' in cars_df.columns:
            unique_models = cars_df['model_name'].dropna().nunique()
            print(f"✅ Found {unique_models} unique models in sample")
        
        return True
        
    except Exception as e:
        print(f"❌ Error accessing data: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing CarPrice Pro Web Application Components")
    print("=" * 60)
    
    tests = [
        ("Model Loading", test_model_loading),
        ("Prediction", test_prediction),
        ("Data Access", test_data_access)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if test_name == "Model Loading":
                success, *_ = test_func()
            else:
                success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The web application should work correctly.")
        print("\nTo run the web application:")
        print("1. Install requirements: pip install -r requirements.txt")
        print("2. Run the app: python app.py")
        print("3. Open browser to: http://localhost:5000")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the issues above.")

if __name__ == '__main__':
    main()
