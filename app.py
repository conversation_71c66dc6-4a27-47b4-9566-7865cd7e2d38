#!/usr/bin/env python3
"""
Used Cars Price Predictor Web Application
Flask-based web app for predicting used car prices using trained ML model
"""

from flask import Flask, render_template, request, jsonify
import pandas as pd
import numpy as np
import pickle
import os
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# Global variables for model and preprocessing components
model = None
label_encoders = None
feature_columns = None
model_metadata = None

def load_model_components():
    """Load the trained model and preprocessing components"""
    global model, label_encoders, feature_columns, model_metadata

    try:
        # Load model
        with open('models/lgb_model.pkl', 'rb') as f:
            model = pickle.load(f)

        # Load label encoders
        with open('models/label_encoders.pkl', 'rb') as f:
            label_encoders = pickle.load(f)

        # Load feature columns
        with open('models/feature_columns.pkl', 'rb') as f:
            feature_columns = pickle.load(f)

        # Load model metadata
        with open('models/model_metadata.pkl', 'rb') as f:
            model_metadata = pickle.load(f)

        logger.info(f"Model components loaded successfully. R² = {model_metadata.get('test_r2', 'N/A')}")
        return True

    except Exception as e:
        logger.error(f"Error loading model components: {e}")
        return False

def get_unique_values():
    """Get unique values for dropdowns from the dataset sample"""
    try:
        # Load a sample of the dataset to get unique values
        df_path = r"C:\Users\<USER>\.cache\kagglehub\datasets\ananaymital\us-used-cars-dataset\versions\1\used_cars_data.csv"
        cars_df = pd.read_csv(df_path, nrows=50000, low_memory=False)
        
        # Get unique makes (sorted)
        makes = sorted(cars_df['make_name'].dropna().unique().tolist())
        
        # Get models by make
        models_by_make = {}
        for make in makes:
            models = sorted(cars_df[cars_df['make_name'] == make]['model_name'].dropna().unique().tolist())
            models_by_make[make] = models
        
        # Get year range
        years = list(range(2000, 2022))  # Focus on recent years
        
        return {
            'makes': makes,
            'models_by_make': models_by_make,
            'years': years
        }
    except Exception as e:
        logger.error(f"Error getting unique values: {e}")
        return {
            'makes': ['Toyota', 'Honda', 'Ford', 'Chevrolet', 'BMW'],
            'models_by_make': {
                'Toyota': ['Camry', 'Corolla', 'Prius', 'RAV4'],
                'Honda': ['Civic', 'Accord', 'CR-V', 'Pilot'],
                'Ford': ['F-150', 'Focus', 'Escape', 'Mustang'],
                'Chevrolet': ['Silverado', 'Cruze', 'Equinox', 'Malibu'],
                'BMW': ['3 Series', '5 Series', 'X3', 'X5']
            },
            'years': list(range(2000, 2022))
        }

@app.route('/')
def home():
    """Home page"""
    return render_template('home.html')

@app.route('/predictor')
def predictor():
    """Price predictor page"""
    unique_values = get_unique_values()
    return render_template('predictor.html', **unique_values)

@app.route('/insights')
def insights():
    """Data insights page"""
    return render_template('insights.html')

@app.route('/about')
def about():
    """About page"""
    return render_template('about.html')

@app.route('/api/models/<make>')
def get_models(make):
    """API endpoint to get models for a specific make"""
    unique_values = get_unique_values()
    models = unique_values['models_by_make'].get(make, [])
    return jsonify(models)

@app.route('/api/predict', methods=['POST'])
def predict_price():
    """API endpoint for price prediction"""
    try:
        # Get input data
        data = request.json
        make = data.get('make')
        model_name = data.get('model')
        year = int(data.get('year'))
        mileage = float(data.get('mileage'))
        
        # Validate inputs
        if not all([make, model_name, year, mileage]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        if year < 1990 or year > 2022:
            return jsonify({'error': 'Year must be between 1990 and 2022'}), 400
        
        if mileage < 0 or mileage > 500000:
            return jsonify({'error': 'Mileage must be between 0 and 500,000'}), 400
        
        # Check if model is loaded
        if model is None:
            if not load_model_components():
                return jsonify({'error': 'Model not available'}), 500
        
        # Create prediction input
        prediction_input = create_prediction_input(make, model_name, year, mileage)
        
        # Make prediction
        predicted_price = model.predict([prediction_input])[0]
        
        # Format response
        response = {
            'predicted_price': round(predicted_price, 2),
            'formatted_price': f"${predicted_price:,.2f}",
            'input_summary': {
                'make': make,
                'model': model_name,
                'year': year,
                'mileage': f"{mileage:,.0f}"
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Prediction error: {e}")
        return jsonify({'error': 'Prediction failed'}), 500

def create_prediction_input(make, model_name, year, mileage):
    """Create input vector for prediction using trained model features"""
    current_year = 2021
    age = current_year - year

    # Create feature dictionary with realistic defaults based on training data
    features = {
        'mileage': mileage,
        'age': age,
        'year': year,
        'make_name': make,
        'model_name': model_name,
        # Default values based on typical car characteristics
        'horsepower': get_default_horsepower(make, model_name),
        'engine_displacement': get_default_displacement(make, model_name),
        'city_fuel_economy': get_default_city_mpg(make, model_name),
        'highway_fuel_economy': get_default_highway_mpg(make, model_name),
        'daysonmarket': 30,
        'latitude': 39.0,  # Center of US
        'longitude': -98.0,
        'seller_rating': 4.2,  # Average seller rating
        'body_type': get_default_body_type(make, model_name),
        'fuel_type': 'Gasoline',
        'transmission': 'Automatic',
        'exterior_color': 'White',
        'franchise_dealer': True
    }

    # Calculate engineered features
    features['mileage_per_year'] = mileage / (age + 1)
    features['power_to_displacement'] = features['horsepower'] / (features['engine_displacement'] + 1)
    features['avg_fuel_economy'] = (features['city_fuel_economy'] + features['highway_fuel_economy']) / 2

    # Convert to the format expected by the model
    feature_vector = []
    for col in feature_columns:
        if col in features:
            value = features[col]
            # Apply label encoding for categorical variables
            if col in label_encoders and isinstance(value, str):
                try:
                    value = label_encoders[col].transform([str(value)])[0]
                except:
                    # Use the first class if unseen (most common approach)
                    value = 0
            elif col in label_encoders and isinstance(value, bool):
                value = int(value)
            feature_vector.append(float(value))
        else:
            feature_vector.append(0.0)  # Default value

    return feature_vector

def get_default_horsepower(make, model_name):
    """Get default horsepower based on make/model"""
    defaults = {
        'Toyota': 180, 'Honda': 170, 'Ford': 200, 'Chevrolet': 210,
        'BMW': 250, 'Mercedes-Benz': 260, 'Audi': 240, 'Lexus': 220,
        'Nissan': 175, 'Hyundai': 165, 'Kia': 160, 'Mazda': 170,
        'Subaru': 180, 'Volkswagen': 190, 'Acura': 200, 'Infiniti': 210
    }
    return defaults.get(make, 185)

def get_default_displacement(make, model_name):
    """Get default engine displacement based on make/model"""
    defaults = {
        'Toyota': 2.4, 'Honda': 2.0, 'Ford': 2.5, 'Chevrolet': 2.8,
        'BMW': 3.0, 'Mercedes-Benz': 3.2, 'Audi': 2.8, 'Lexus': 2.6,
        'Nissan': 2.2, 'Hyundai': 2.0, 'Kia': 2.0, 'Mazda': 2.0,
        'Subaru': 2.5, 'Volkswagen': 2.0, 'Acura': 2.4, 'Infiniti': 2.6
    }
    return defaults.get(make, 2.4)

def get_default_city_mpg(make, model_name):
    """Get default city MPG based on make/model"""
    defaults = {
        'Toyota': 28, 'Honda': 30, 'Ford': 24, 'Chevrolet': 22,
        'BMW': 20, 'Mercedes-Benz': 19, 'Audi': 21, 'Lexus': 23,
        'Nissan': 26, 'Hyundai': 28, 'Kia': 27, 'Mazda': 26,
        'Subaru': 24, 'Volkswagen': 25, 'Acura': 24, 'Infiniti': 21
    }
    return defaults.get(make, 25)

def get_default_highway_mpg(make, model_name):
    """Get default highway MPG based on make/model"""
    defaults = {
        'Toyota': 35, 'Honda': 38, 'Ford': 32, 'Chevrolet': 30,
        'BMW': 28, 'Mercedes-Benz': 27, 'Audi': 29, 'Lexus': 31,
        'Nissan': 34, 'Hyundai': 36, 'Kia': 35, 'Mazda': 34,
        'Subaru': 32, 'Volkswagen': 33, 'Acura': 32, 'Infiniti': 29
    }
    return defaults.get(make, 32)

def get_default_body_type(make, model_name):
    """Get default body type based on make/model"""
    if any(suv in model_name.lower() for suv in ['suv', 'x3', 'x5', 'rav4', 'cr-v', 'escape']):
        return 'SUV'
    elif any(truck in model_name.lower() for truck in ['f-150', 'silverado', 'ram', 'truck']):
        return 'Pickup Truck'
    elif any(coupe in model_name.lower() for coupe in ['coupe', 'mustang', 'camaro']):
        return 'Coupe'
    else:
        return 'Sedan'

if __name__ == '__main__':
    # Create models directory if it doesn't exist
    os.makedirs('models', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    
    # Try to load model components
    load_model_components()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
