# CarPrice Pro - AI-Powered Used Car Price Predictor

A sophisticated web application that uses machine learning to predict used car prices with 95.5% accuracy. Built with Flask, LightGBM, and modern web technologies.

![CarPrice Pro](https://img.shields.io/badge/Accuracy-95.5%25-brightgreen) ![Python](https://img.shields.io/badge/Python-3.8+-blue) ![Flask](https://img.shields.io/badge/Flask-2.3+-red) ![LightGBM](https://img.shields.io/badge/LightGBM-4.0+-orange)

## 🚀 Features

- **AI-Powered Predictions**: LightGBM model trained on 3M+ car listings
- **95.5% Accuracy**: Industry-leading R² score of 0.955
- **Instant Results**: Get price estimates in seconds
- **Interactive Interface**: Modern, responsive web design
- **Market Insights**: Comprehensive data analysis and visualizations
- **Mobile Friendly**: Works seamlessly on all devices

## 📊 Model Performance

- **R² Score**: 0.955 (95.5% variance explained)
- **Mean Absolute Error**: $2,215
- **Root Mean Square Error**: $3,294
- **Training Data**: 3M+ car listings (2005-2020)
- **Features**: 21 engineered features including make, model, year, mileage, and more

## 🛠️ Technology Stack

- **Backend**: Python, Flask, LightGBM, scikit-learn
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Data Processing**: pandas, numpy
- **Visualization**: Chart.js
- **Model**: Microsoft LightGBM gradient boosting

## 📁 Project Structure

```
CarPrice Pro/
├── app.py                 # Main Flask application
├── train_model.py         # Model training script
├── test_app.py           # Application testing script
├── requirements.txt      # Python dependencies
├── models/               # Trained model artifacts
│   ├── lgb_model.pkl
│   ├── label_encoders.pkl
│   ├── feature_columns.pkl
│   └── model_metadata.pkl
├── templates/            # HTML templates
│   ├── base.html
│   ├── home.html
│   ├── predictor.html
│   ├── insights.html
│   └── about.html
└── static/              # Static assets
    ├── css/
    │   └── style.css
    └── js/
        └── main.js
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- pip package manager
- Access to the US Used Cars Dataset (automatically downloaded)

### Installation

1. **Clone or download the project files**

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Train the model** (first time only)
   ```bash
   python train_model.py
   ```
   This will:
   - Download the dataset (if not already present)
   - Train the LightGBM model
   - Save all model components to the `models/` directory

4. **Test the application** (optional)
   ```bash
   python test_app.py
   ```

5. **Run the web application**
   ```bash
   python app.py
   ```

6. **Open your browser** and navigate to:
   ```
   http://localhost:5000
   ```

## 🎯 Usage

### Price Prediction

1. Navigate to the **Price Predictor** page
2. Select the car's **Make** from the dropdown
3. Choose the **Model** (dynamically populated based on make)
4. Enter the **Year** (1990-2022)
5. Input the **Mileage** (0-500,000)
6. Click **Predict Price** to get an instant estimate

### Market Insights

Visit the **Market Insights** page to explore:
- Price distribution analysis
- Top car brands by popularity
- Depreciation patterns
- Fuel economy vs. price relationships
- Feature importance rankings

## 🔧 API Endpoints

### GET Routes
- `/` - Home page
- `/predictor` - Price prediction interface
- `/insights` - Market insights and analytics
- `/about` - About page and model information

### API Routes
- `GET /api/models/<make>` - Get models for a specific make
- `POST /api/predict` - Make price prediction

#### Prediction API Example
```javascript
// POST /api/predict
{
  "make": "Toyota",
  "model": "Camry",
  "year": 2018,
  "mileage": 50000
}

// Response
{
  "predicted_price": 23450.75,
  "formatted_price": "$23,450.75",
  "input_summary": {
    "make": "Toyota",
    "model": "Camry", 
    "year": 2018,
    "mileage": "50,000"
  }
}
```

## 🧠 Model Details

### Algorithm
- **LightGBM**: Microsoft's gradient boosting framework
- **Objective**: Regression with RMSE optimization
- **Features**: 21 engineered features
- **Training**: 80/20 train-test split with proper validation

### Key Features Used
1. **Vehicle Characteristics**: Make, model, year, body type
2. **Usage Metrics**: Mileage, age, mileage per year
3. **Performance**: Horsepower, engine displacement, power-to-displacement ratio
4. **Efficiency**: City/highway/average fuel economy
5. **Market Factors**: Days on market, geographic location, seller rating
6. **Additional**: Transmission, fuel type, exterior color, dealer type

### Data Preprocessing
- Missing value imputation using training data statistics
- Label encoding for categorical variables
- Feature engineering for derived metrics
- Outlier removal (0.5th-99.5th percentile)
- Proper train/test split to prevent data leakage

## 📈 Performance Metrics

| Metric | Value |
|--------|-------|
| R² Score | 0.955 |
| Mean Absolute Error | $2,215 |
| Root Mean Square Error | $3,294 |
| Average Error Rate | 7.4% |
| Training Records | 3M+ |
| Test Set Size | 594K+ |

## 🎨 Features

### Web Interface
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern UI**: Clean, professional interface with Bootstrap 5
- **Interactive Charts**: Dynamic visualizations with Chart.js
- **Real-time Validation**: Client-side form validation
- **Loading States**: Visual feedback during predictions
- **Error Handling**: User-friendly error messages

### Technical Features
- **Model Caching**: Efficient model loading and caching
- **API Design**: RESTful API endpoints
- **Data Validation**: Comprehensive input validation
- **Error Recovery**: Graceful handling of edge cases
- **Performance**: Optimized for fast predictions

## 🔍 Testing

Run the test suite to verify all components:

```bash
python test_app.py
```

Tests include:
- Model loading verification
- Prediction functionality
- Data access validation
- Component integration

## 🚀 Deployment

### Local Development
```bash
python app.py
```

### Production Deployment
1. Install production WSGI server:
   ```bash
   pip install gunicorn
   ```

2. Run with Gunicorn:
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

### Environment Variables
- `FLASK_ENV`: Set to `production` for production deployment
- `SECRET_KEY`: Set a secure secret key for production

## 📊 Dataset Information

- **Source**: US Used Cars Dataset (Kaggle)
- **Size**: 3M+ records
- **Time Period**: 2005-2020
- **Features**: 66 original features
- **Coverage**: All 50 US states
- **Brands**: 59 major car manufacturers

## 🤝 Contributing

This project was built as a capstone demonstration of machine learning and web development skills. The codebase is well-documented and modular for easy understanding and extension.

## 📄 License

This project is for educational and demonstration purposes.

## 🙏 Acknowledgments

- **Dataset**: US Used Cars Dataset by Ananaymital on Kaggle
- **Framework**: Microsoft LightGBM team
- **UI Components**: Bootstrap team
- **Visualization**: Chart.js contributors

---

**Built with ❤️ using Python, Flask, and LightGBM**

For questions or support, please refer to the code documentation and comments throughout the project files.
