{% extends "base.html" %}

{% block title %}Price Predictor - Used Cars Price Predictor{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-12">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-calculator text-warning"></i>
                    Car Price Predictor
                </h1>
                <p class="lead">
                    Get an instant AI-powered price estimate for any used car
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Prediction Form Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Prediction Form -->
                <div class="prediction-form mb-4">
                    <h3 class="text-center mb-4">
                        <i class="fas fa-car text-primary"></i>
                        Enter Car Details
                    </h3>
                    
                    <form id="predictionForm">
                        <div class="row g-3">
                            <!-- Make Selection -->
                            <div class="col-md-6">
                                <label for="make" class="form-label fw-bold">
                                    <i class="fas fa-industry"></i> Make
                                </label>
                                <select class="form-select" id="make" name="make" required>
                                    <option value="">Select Make</option>
                                    {% for make in makes %}
                                    <option value="{{ make }}">{{ make }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Model Selection -->
                            <div class="col-md-6">
                                <label for="model" class="form-label fw-bold">
                                    <i class="fas fa-car-side"></i> Model
                                </label>
                                <select class="form-select" id="model" name="model" required disabled>
                                    <option value="">Select Make First</option>
                                </select>
                            </div>
                            
                            <!-- Year Selection -->
                            <div class="col-md-6">
                                <label for="year" class="form-label fw-bold">
                                    <i class="fas fa-calendar-alt"></i> Year
                                </label>
                                <select class="form-select" id="year" name="year" required>
                                    <option value="">Select Year</option>
                                    {% for year in years|reverse %}
                                    <option value="{{ year }}">{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Mileage Input -->
                            <div class="col-md-6">
                                <label for="mileage" class="form-label fw-bold">
                                    <i class="fas fa-road"></i> Mileage
                                </label>
                                <input type="number" class="form-control" id="mileage" name="mileage" 
                                       placeholder="Enter mileage" min="0" max="500000" required>
                                <div class="form-text">Enter the current mileage (0 - 500,000)</div>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg px-5">
                                <i class="fas fa-magic"></i> Predict Price
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Loading Spinner -->
                <div id="loadingSpinner" class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Analyzing car data...</p>
                </div>
                
                <!-- Error Message -->
                <div id="errorMessage" class="error-message" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorText">An error occurred</span>
                </div>
                
                <!-- Prediction Result -->
                <div id="predictionResult" class="prediction-result fade-in-up" style="display: none;">
                    <h2 id="predictedPrice">$0</h2>
                    <p class="lead mb-3">Estimated Market Value</p>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="result-detail">
                                <i class="fas fa-industry fa-2x mb-2"></i>
                                <h6>Make</h6>
                                <p id="resultMake">-</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="result-detail">
                                <i class="fas fa-car-side fa-2x mb-2"></i>
                                <h6>Model</h6>
                                <p id="resultModel">-</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="result-detail">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                <h6>Year</h6>
                                <p id="resultYear">-</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="result-detail">
                                <i class="fas fa-road fa-2x mb-2"></i>
                                <h6>Mileage</h6>
                                <p id="resultMileage">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button class="btn btn-outline-light btn-lg me-3" onclick="resetForm()">
                            <i class="fas fa-redo"></i> Predict Another
                        </button>
                        <button class="btn btn-warning btn-lg" onclick="shareResult()">
                            <i class="fas fa-share"></i> Share Result
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Model Info Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h3 class="fw-bold text-primary">About Our AI Model</h3>
                <p class="text-muted">Powered by advanced machine learning technology</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-3 text-center">
                <div class="bg-white p-4 rounded-3 shadow-sm h-100">
                    <i class="fas fa-brain fa-3x text-primary mb-3"></i>
                    <h5 class="fw-bold">LightGBM Algorithm</h5>
                    <p class="text-muted">Advanced gradient boosting for superior accuracy</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="bg-white p-4 rounded-3 shadow-sm h-100">
                    <i class="fas fa-database fa-3x text-success mb-3"></i>
                    <h5 class="fw-bold">3M+ Training Records</h5>
                    <p class="text-muted">Trained on comprehensive car listing data</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="bg-white p-4 rounded-3 shadow-sm h-100">
                    <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                    <h5 class="fw-bold">95.5% Accuracy</h5>
                    <p class="text-muted">Exceptional R² score for reliable predictions</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="bg-white p-4 rounded-3 shadow-sm h-100">
                    <i class="fas fa-dollar-sign fa-3x text-warning mb-3"></i>
                    <h5 class="fw-bold">$2,215 Avg Error</h5>
                    <p class="text-muted">Low mean absolute error for precise estimates</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
// Store models data for dynamic loading
const modelsData = {{ models_by_make|tojson }};

// Form elements
const makeSelect = document.getElementById('make');
const modelSelect = document.getElementById('model');
const yearSelect = document.getElementById('year');
const mileageInput = document.getElementById('mileage');
const predictionForm = document.getElementById('predictionForm');
const loadingSpinner = document.getElementById('loadingSpinner');
const errorMessage = document.getElementById('errorMessage');
const predictionResult = document.getElementById('predictionResult');

// Handle make selection change
makeSelect.addEventListener('change', function() {
    const selectedMake = this.value;
    modelSelect.innerHTML = '<option value="">Select Model</option>';
    
    if (selectedMake && modelsData[selectedMake]) {
        modelSelect.disabled = false;
        modelsData[selectedMake].forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            modelSelect.appendChild(option);
        });
    } else {
        modelSelect.disabled = true;
    }
});

// Handle form submission
predictionForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Hide previous results
    hideAllMessages();
    showLoading();
    
    // Get form data
    const formData = {
        make: makeSelect.value,
        model: modelSelect.value,
        year: yearSelect.value,
        mileage: mileageInput.value
    };
    
    // Validate form data
    if (!formData.make || !formData.model || !formData.year || !formData.mileage) {
        showError('Please fill in all fields');
        return;
    }
    
    try {
        // Make API call
        const response = await fetch('/api/predict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showResult(result);
        } else {
            showError(result.error || 'Prediction failed');
        }
    } catch (error) {
        showError('Network error. Please try again.');
    }
});

function showLoading() {
    loadingSpinner.style.display = 'block';
}

function hideAllMessages() {
    loadingSpinner.style.display = 'none';
    errorMessage.style.display = 'none';
    predictionResult.style.display = 'none';
}

function showError(message) {
    hideAllMessages();
    document.getElementById('errorText').textContent = message;
    errorMessage.style.display = 'block';
}

function showResult(result) {
    hideAllMessages();
    
    // Update result display
    document.getElementById('predictedPrice').textContent = result.formatted_price;
    document.getElementById('resultMake').textContent = result.input_summary.make;
    document.getElementById('resultModel').textContent = result.input_summary.model;
    document.getElementById('resultYear').textContent = result.input_summary.year;
    document.getElementById('resultMileage').textContent = result.input_summary.mileage + ' miles';
    
    predictionResult.style.display = 'block';
    predictionResult.scrollIntoView({ behavior: 'smooth' });
}

function resetForm() {
    predictionForm.reset();
    modelSelect.disabled = true;
    modelSelect.innerHTML = '<option value="">Select Make First</option>';
    hideAllMessages();
}

function shareResult() {
    const price = document.getElementById('predictedPrice').textContent;
    const make = document.getElementById('resultMake').textContent;
    const model = document.getElementById('resultModel').textContent;
    const year = document.getElementById('resultYear').textContent;
    
    const shareText = `Check out this car price estimate: ${year} ${make} ${model} - ${price} (via CarPrice Pro)`;
    
    if (navigator.share) {
        navigator.share({
            title: 'Car Price Estimate',
            text: shareText,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(shareText).then(() => {
            alert('Result copied to clipboard!');
        });
    }
}
</script>
{% endblock %}
