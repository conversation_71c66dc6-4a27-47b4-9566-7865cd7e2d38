# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('default')
sns.set_palette("husl")

print("Libraries imported successfully!")

# Import machine learning libraries
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb

print("ML libraries imported successfully!")

# Download and load the dataset
import kagglehub
path = kagglehub.dataset_download("ananaymital/us-used-cars-dataset")
print("Dataset downloaded to:", path)

# Load the dataset
df_path = path + "/used_cars_data.csv"
cars_df = pd.read_csv(df_path, low_memory=False)
print(f"Dataset loaded successfully!")
print(f"Shape: {cars_df.shape}")
print(f"Memory usage: {cars_df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

# Display basic information about the dataset
cars_df.info()

# Check for missing values
missing_data = cars_df.isnull().sum()
missing_percent = (missing_data / len(cars_df)) * 100
missing_df = pd.DataFrame({
    'Missing_Count': missing_data,
    'Missing_Percentage': missing_percent
}).sort_values('Missing_Percentage', ascending=False)

print("Missing Data Summary (Top 15):")
print(missing_df[missing_df['Missing_Count'] > 0].head(15))

# Check target variable distribution
print(f"Price statistics:")
print(f"Count: {cars_df['price'].count():,}")
print(f"Mean: ${cars_df['price'].mean():.2f}")
print(f"Median: ${cars_df['price'].median():.2f}")
print(f"Min: ${cars_df['price'].min():.2f}")
print(f"Max: ${cars_df['price'].max():.2f}")
print(f"Missing prices: {cars_df['price'].isnull().sum()}")

# Remove rows with missing target variable (safe operation)
print(f"Original dataset size: {len(cars_df):,}")
cars_df = cars_df.dropna(subset=['price'])
print(f"After removing missing prices: {len(cars_df):,}")

# Remove extreme price outliers (safe operation)
Q1 = cars_df['price'].quantile(0.005)
Q99 = cars_df['price'].quantile(0.995)
print(f"Price range (0.5th-99.5th percentile): ${Q1:.2f} - ${Q99:.2f}")

cars_df = cars_df[(cars_df['price'] >= Q1) & (cars_df['price'] <= Q99)]
print(f"After removing price outliers: {len(cars_df):,}")

# Remove columns with >80% missing data (safe operation)
missing_threshold = 0.8
cols_to_drop = missing_df[missing_df['Missing_Percentage'] > missing_threshold * 100].index.tolist()
print(f"Columns to drop (>80% missing): {len(cols_to_drop)}")

cars_df = cars_df.drop(columns=cols_to_drop)
print(f"Remaining columns: {cars_df.shape[1]}")

# Create basic age feature (safe transformation)
current_year = 2021
cars_df['age'] = current_year - cars_df['year']
print(f"Age feature created. Range: {cars_df['age'].min()} - {cars_df['age'].max()} years")

# Select features for modeling
feature_columns = [
    # Numerical features
    'mileage', 'age', 'horsepower', 'engine_displacement', 
    'city_fuel_economy', 'highway_fuel_economy', 'daysonmarket',
    'latitude', 'longitude', 'seller_rating', 'year',
    
    # Categorical features
    'make_name', 'model_name', 'body_type', 'fuel_type', 'transmission',
    'exterior_color', 'franchise_dealer'
]

# Keep only available columns
available_features = [col for col in feature_columns if col in cars_df.columns]
print(f"Selected {len(available_features)} features for modeling")

# Prepare data for modeling
X = cars_df[available_features].copy()
y = cars_df['price'].copy()

print(f"Feature matrix shape: {X.shape}")
print(f"Target vector shape: {y.shape}")

# 🔒 CRITICAL: Train/test split BEFORE any data-dependent preprocessing
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

print(f"Training set size: {X_train.shape[0]:,}")
print(f"Test set size: {X_test.shape[0]:,}")
print(f"Training target mean: ${y_train.mean():.2f}")
print(f"Test target mean: ${y_test.mean():.2f}")

# Identify column types from training data
categorical_cols = X_train.select_dtypes(include=['object', 'category', 'bool']).columns.tolist()
numerical_cols = X_train.select_dtypes(include=[np.number]).columns.tolist()

print(f"Categorical columns: {len(categorical_cols)}")
print(f"Numerical columns: {len(numerical_cols)}")
print(f"Categorical: {categorical_cols}")
print(f"Numerical: {numerical_cols}")

# Handle missing values using ONLY training data statistics
print("Imputing missing values using training data only...")

# Numerical imputation - fit on training, transform both
numerical_imputer = SimpleImputer(strategy='median')
X_train_num = X_train[numerical_cols].copy()
X_test_num = X_test[numerical_cols].copy()

X_train_num_imputed = pd.DataFrame(
    numerical_imputer.fit_transform(X_train_num),
    columns=numerical_cols,
    index=X_train_num.index
)

X_test_num_imputed = pd.DataFrame(
    numerical_imputer.transform(X_test_num),
    columns=numerical_cols,
    index=X_test_num.index
)

print("✅ Numerical imputation completed")

# Categorical imputation - fit on training, transform both
categorical_imputer = SimpleImputer(strategy='constant', fill_value='Unknown')
X_train_cat = X_train[categorical_cols].copy()
X_test_cat = X_test[categorical_cols].copy()

X_train_cat_imputed = pd.DataFrame(
    categorical_imputer.fit_transform(X_train_cat),
    columns=categorical_cols,
    index=X_train_cat.index
)

X_test_cat_imputed = pd.DataFrame(
    categorical_imputer.transform(X_test_cat),
    columns=categorical_cols,
    index=X_test_cat.index
)

print("✅ Categorical imputation completed")

# Combine imputed features
X_train_clean = pd.concat([X_train_num_imputed, X_train_cat_imputed], axis=1)
X_test_clean = pd.concat([X_test_num_imputed, X_test_cat_imputed], axis=1)

print(f"Training data shape after imputation: {X_train_clean.shape}")
print(f"Test data shape after imputation: {X_test_clean.shape}")
print("✅ Data imputation completed without leakage")

# Create engineered features on split data
print("Creating engineered features...")

# Mileage per year
if 'mileage' in X_train_clean.columns and 'age' in X_train_clean.columns:
    X_train_clean['mileage_per_year'] = X_train_clean['mileage'] / (X_train_clean['age'] + 1)
    X_test_clean['mileage_per_year'] = X_test_clean['mileage'] / (X_test_clean['age'] + 1)
    print("✅ Mileage per year feature created")

# Power to displacement ratio
if 'horsepower' in X_train_clean.columns and 'engine_displacement' in X_train_clean.columns:
    X_train_clean['power_to_displacement'] = X_train_clean['horsepower'] / (X_train_clean['engine_displacement'] + 1)
    X_test_clean['power_to_displacement'] = X_test_clean['horsepower'] / (X_test_clean['engine_displacement'] + 1)
    print("✅ Power-to-displacement ratio created")

# Average fuel economy
if 'city_fuel_economy' in X_train_clean.columns and 'highway_fuel_economy' in X_train_clean.columns:
    X_train_clean['avg_fuel_economy'] = (X_train_clean['city_fuel_economy'] + X_train_clean['highway_fuel_economy']) / 2
    X_test_clean['avg_fuel_economy'] = (X_test_clean['city_fuel_economy'] + X_test_clean['highway_fuel_economy']) / 2
    print("✅ Average fuel economy created")

print(f"Final feature count: {X_train_clean.shape[1]}")

# Categorical encoding using only training data
print("Encoding categorical variables...")

# Update categorical columns list
categorical_cols = X_train_clean.select_dtypes(include=['object', 'category', 'bool']).columns.tolist()
X_train_encoded = X_train_clean.copy()
X_test_encoded = X_test_clean.copy()

label_encoders = {}
for col in categorical_cols:
    le = LabelEncoder()
    
    # Fit only on training data
    X_train_encoded[col] = le.fit_transform(X_train_encoded[col].astype(str))
    
    # Handle unseen labels in test set
    test_values = X_test_encoded[col].astype(str)
    unseen_mask = ~test_values.isin(le.classes_)
    
    if unseen_mask.any():
        # Use most frequent from TRAINING data only
        most_frequent = X_train_clean[col].mode()[0] if not X_train_clean[col].mode().empty else 'Unknown'
        test_values[unseen_mask] = str(most_frequent)
        print(f"Replaced {unseen_mask.sum()} unseen labels in {col}")
    
    X_test_encoded[col] = le.transform(test_values)
    label_encoders[col] = le

print("✅ Categorical encoding completed without leakage")

# Create validation split from training data only (NO test data leakage)
X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
    X_train_encoded, y_train, test_size=0.2, random_state=42
)

print(f"Training split: {X_train_split.shape[0]:,}")
print(f"Validation split: {X_val_split.shape[0]:,}")
print(f"Test set (untouched): {X_test_encoded.shape[0]:,}")

# Train LightGBM model with proper validation
print("Training LightGBM model...")

lgb_params = {
    'objective': 'regression',
    'metric': 'rmse',
    'boosting_type': 'gbdt',
    'num_leaves': 150,
    'learning_rate': 0.1,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': -1,
    'random_state': 42
}

# Use validation set from training data (NOT test set)
train_data = lgb.Dataset(X_train_split, label=y_train_split)
valid_data = lgb.Dataset(X_val_split, label=y_val_split, reference=train_data)

lgb_model = lgb.train(
    lgb_params,
    train_data,
    valid_sets=[train_data, valid_data],
    num_boost_round=1000,
    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(100)]
)

print("✅ Model training completed")

# Final evaluation on test set (first time test set is used for predictions)
print("Final model evaluation...")

y_pred_train = lgb_model.predict(X_train_encoded)
y_pred_test = lgb_model.predict(X_test_encoded)

# Calculate metrics
train_r2 = r2_score(y_train, y_pred_train)
test_r2 = r2_score(y_test, y_pred_test)
test_mae = mean_absolute_error(y_test, y_pred_test)
test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))

print("✅ Evaluation completed")

# Display final results
print(f"\n{'='*60}")
print("🔒 FINAL RESULTS (No Data Leakage)")
print(f"{'='*60}")
print(f"Training R²:     {train_r2:.4f}")
print(f"Test R²:         {test_r2:.4f}")
print(f"Test MAE:        ${test_mae:.2f}")
print(f"Test RMSE:       ${test_rmse:.2f}")
print()
print(f"🎯 TARGET:       R² ≥ 0.85")
print(f"{'✅' if test_r2 >= 0.85 else '❌'} ACHIEVED:     R² = {test_r2:.4f}")

if test_r2 >= 0.85:
    print(f"🚀 EXCEEDED BY:  {((test_r2 - 0.85) / 0.85 * 100):.1f}%")
    print("\n🎉 SUCCESS: Target achieved without data leakage!")
else:
    print(f"📉 SHORTFALL:    {((0.85 - test_r2) / 0.85 * 100):.1f}%")
    print("\n⚠️ Target not achieved. Need optimization.")

print(f"\n🔒 DATA LEAKAGE PREVENTION:")
print("✅ Train/test split before preprocessing")
print("✅ Imputation using only training statistics")
print("✅ Feature engineering on split data")
print("✅ Validation set from training data only")
print("✅ Test set used only for final evaluation")
print(f"{'='*60}")