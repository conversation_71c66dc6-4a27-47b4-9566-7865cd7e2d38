#!/usr/bin/env python3
"""
Used Cars Price Prediction Analysis - CORRECTED VERSION (No Data Leakage)
Goal: Achieve R² ≥ 0.85 without data leakage
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from sklearn.preprocessing import LabelEncoder
from sklearn.impute import SimpleImputer
import lightgbm as lgb

print("=== USED CARS PRICE PREDICTION PROJECT (CORRECTED) ===")
print("🎯 Goal: Achieve R² ≥ 0.85 WITHOUT data leakage")
print()

# 1. LOAD DATA
print("1. Loading dataset...")
df_path = r"C:\Users\<USER>\.cache\kagglehub\datasets\ananaymital\us-used-cars-dataset\versions\1\used_cars_data.csv"
cars_df = pd.read_csv(df_path, low_memory=False)
print(f"✅ Dataset loaded: {cars_df.shape}")

# 2. INITIAL DATA CLEANING (Safe operations before split)
print("\n2. Initial data cleaning...")
print(f"Original size: {len(cars_df):,}")

# Remove rows with missing target variable
cars_df = cars_df.dropna(subset=['price'])
print(f"After removing missing prices: {len(cars_df):,}")

# Remove extreme outliers (0.5th to 99.5th percentile)
Q1 = cars_df['price'].quantile(0.005)
Q99 = cars_df['price'].quantile(0.995)
cars_df = cars_df[(cars_df['price'] >= Q1) & (cars_df['price'] <= Q99)]
print(f"After removing outliers: {len(cars_df):,}")

# Remove columns with >80% missing data (safe before split)
missing_percent = (cars_df.isnull().sum() / len(cars_df)) * 100
cols_to_drop = missing_percent[missing_percent > 80].index.tolist()
cars_df = cars_df.drop(columns=cols_to_drop)
print(f"Dropped {len(cols_to_drop)} sparse columns")

# Create basic age feature (safe transformation)
current_year = 2021
cars_df['age'] = current_year - cars_df['year']

# 3. FEATURE SELECTION (before split)
print("\n3. Feature selection...")
feature_columns = [
    # Numerical features
    'mileage', 'age', 'horsepower', 'engine_displacement', 
    'city_fuel_economy', 'highway_fuel_economy', 'daysonmarket',
    'latitude', 'longitude', 'seller_rating', 'year',
    
    # Categorical features
    'make_name', 'model_name', 'body_type', 'fuel_type', 'transmission',
    'exterior_color', 'franchise_dealer'
]

available_features = [col for col in feature_columns if col in cars_df.columns]
X = cars_df[available_features].copy()
y = cars_df['price'].copy()

print(f"Selected {len(available_features)} features")
print(f"Dataset shape: {X.shape}")

# 4. TRAIN/TEST SPLIT (Critical: Do this BEFORE any data-dependent preprocessing)
print("\n4. Train/test split...")
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=None
)

print(f"Training set: {X_train.shape[0]:,}")
print(f"Test set: {X_test.shape[0]:,}")

# 5. DATA PREPROCESSING (Only using training data statistics)
print("\n5. Data preprocessing (no leakage)...")

# Identify column types
categorical_cols = X_train.select_dtypes(include=['object', 'category', 'bool']).columns.tolist()
numerical_cols = X_train.select_dtypes(include=[np.number]).columns.tolist()

print(f"Categorical columns: {len(categorical_cols)}")
print(f"Numerical columns: {len(numerical_cols)}")

# Handle missing values using ONLY training data statistics
print("\nImputing missing values...")

# Numerical imputation
numerical_imputer = SimpleImputer(strategy='median')
X_train_num = X_train[numerical_cols].copy()
X_test_num = X_test[numerical_cols].copy()

# Fit on training data only, transform both
X_train_num_imputed = pd.DataFrame(
    numerical_imputer.fit_transform(X_train_num),
    columns=numerical_cols,
    index=X_train_num.index
)

X_test_num_imputed = pd.DataFrame(
    numerical_imputer.transform(X_test_num),
    columns=numerical_cols,
    index=X_test_num.index
)

# Categorical imputation
categorical_imputer = SimpleImputer(strategy='constant', fill_value='Unknown')
X_train_cat = X_train[categorical_cols].copy()
X_test_cat = X_test[categorical_cols].copy()

X_train_cat_imputed = pd.DataFrame(
    categorical_imputer.fit_transform(X_train_cat),
    columns=categorical_cols,
    index=X_train_cat.index
)

X_test_cat_imputed = pd.DataFrame(
    categorical_imputer.transform(X_test_cat),
    columns=categorical_cols,
    index=X_test_cat.index
)

# Combine imputed features
X_train_clean = pd.concat([X_train_num_imputed, X_train_cat_imputed], axis=1)
X_test_clean = pd.concat([X_test_num_imputed, X_test_cat_imputed], axis=1)

print("✅ Missing value imputation completed")

# 6. FEATURE ENGINEERING (Using only training data statistics)
print("\n6. Feature engineering (no leakage)...")

# Create engineered features using training data patterns
if 'mileage' in X_train_clean.columns and 'age' in X_train_clean.columns:
    X_train_clean['mileage_per_year'] = X_train_clean['mileage'] / (X_train_clean['age'] + 1)
    X_test_clean['mileage_per_year'] = X_test_clean['mileage'] / (X_test_clean['age'] + 1)

if 'horsepower' in X_train_clean.columns and 'engine_displacement' in X_train_clean.columns:
    X_train_clean['power_to_displacement'] = X_train_clean['horsepower'] / (X_train_clean['engine_displacement'] + 1)
    X_test_clean['power_to_displacement'] = X_test_clean['horsepower'] / (X_test_clean['engine_displacement'] + 1)

if 'city_fuel_economy' in X_train_clean.columns and 'highway_fuel_economy' in X_train_clean.columns:
    X_train_clean['avg_fuel_economy'] = (X_train_clean['city_fuel_economy'] + X_train_clean['highway_fuel_economy']) / 2
    X_test_clean['avg_fuel_economy'] = (X_test_clean['city_fuel_economy'] + X_test_clean['highway_fuel_economy']) / 2

print("✅ Feature engineering completed")

# 7. CATEGORICAL ENCODING (Using only training data)
print("\n7. Categorical encoding (no leakage)...")

# Update categorical columns list
categorical_cols = X_train_clean.select_dtypes(include=['object', 'category', 'bool']).columns.tolist()
X_train_encoded = X_train_clean.copy()
X_test_encoded = X_test_clean.copy()

label_encoders = {}
for col in categorical_cols:
    le = LabelEncoder()
    
    # Fit only on training data
    X_train_encoded[col] = le.fit_transform(X_train_encoded[col].astype(str))
    
    # Handle unseen labels in test set
    test_values = X_test_encoded[col].astype(str)
    unseen_mask = ~test_values.isin(le.classes_)
    
    if unseen_mask.any():
        # Use most frequent from TRAINING data only
        most_frequent = X_train_clean[col].mode()[0] if not X_train_clean[col].mode().empty else 'Unknown'
        test_values[unseen_mask] = str(most_frequent)
        print(f"Replaced {unseen_mask.sum()} unseen labels in {col}")
    
    X_test_encoded[col] = le.transform(test_values)
    label_encoders[col] = le

print("✅ Categorical encoding completed")

# 8. MODEL TRAINING (No test data leakage)
print("\n8. Training LightGBM model...")

# Create validation split from training data only
X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
    X_train_encoded, y_train, test_size=0.2, random_state=42
)

lgb_params = {
    'objective': 'regression',
    'metric': 'rmse',
    'boosting_type': 'gbdt',
    'num_leaves': 150,
    'learning_rate': 0.1,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': -1,
    'random_state': 42
}

# Use proper validation set (NOT test set)
train_data = lgb.Dataset(X_train_split, label=y_train_split)
valid_data = lgb.Dataset(X_val_split, label=y_val_split, reference=train_data)

lgb_model = lgb.train(
    lgb_params,
    train_data,
    valid_sets=[train_data, valid_data],
    num_boost_round=1000,
    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
)

print("✅ Model training completed")

# 9. EVALUATION (Test set used only for final evaluation)
print("\n9. Final evaluation...")

y_pred_train = lgb_model.predict(X_train_encoded)
y_pred_test = lgb_model.predict(X_test_encoded)

train_r2 = r2_score(y_train, y_pred_train)
test_r2 = r2_score(y_test, y_pred_test)
test_mae = mean_absolute_error(y_test, y_pred_test)
test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))

# 10. RESULTS
print(f"\n{'='*60}")
print("🔒 CORRECTED RESULTS (No Data Leakage)")
print(f"{'='*60}")
print(f"Training R²:     {train_r2:.4f}")
print(f"Test R²:         {test_r2:.4f}")
print(f"Test MAE:        ${test_mae:.2f}")
print(f"Test RMSE:       ${test_rmse:.2f}")
print()
print(f"🎯 TARGET:       R² ≥ 0.85")
print(f"{'✅' if test_r2 >= 0.85 else '❌'} ACHIEVED:     R² = {test_r2:.4f}")

if test_r2 >= 0.85:
    print(f"🚀 EXCEEDED BY:  {((test_r2 - 0.85) / 0.85 * 100):.1f}%")
    print("\n🎉 SUCCESS: Target achieved without data leakage!")
else:
    print(f"📉 SHORTFALL:    {((0.85 - test_r2) / 0.85 * 100):.1f}%")
    print("\n⚠️ Target not achieved. Need optimization.")

print(f"\n🔒 DATA LEAKAGE PREVENTION:")
print("✅ Train/test split before preprocessing")
print("✅ Imputation using only training statistics")
print("✅ Feature engineering on split data")
print("✅ Validation set from training data only")
print("✅ Test set used only for final evaluation")

print(f"\n{'='*60}")
print("Corrected analysis completed!")
